# 微信聊天附件归档工具

这个工具可以帮助您整理微信聊天中的附件文件，将散乱的文件按类型归档，并自动去除重复文件。

## 功能特点

- 🗂️ **按类型分类**: 自动将文件按类型分类到不同目录
  - `images`: 图片文件 (jpg, png, gif等)
  - `documents`: 文档文件 (pdf, doc, xls等)
  - `videos`: 视频文件 (mp4, mov, avi等)
  - `audio`: 音频文件 (mp3, wav, aac等)
  - `archives`: 压缩文件 (zip, rar, 7z等)
  - `others`: 其他类型文件

- 🔍 **智能去重**: 基于文件内容的MD5哈希值去重，确保相同文件只保留一份
- 📊 **详细统计**: 提供完整的归档统计信息
- 🛡️ **安全操作**: 不会覆盖已存在的文件，自动重命名
- 📝 **日志记录**: 详细的操作日志，便于追踪

## 使用方法

### 基本用法

```bash
python3 wechat_file_organizer.py
```

这将使用默认设置：
- 源目录: `/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9`
- 目标目录: `/Users/<USER>/Downloads/wechat_files`

### 自定义目录

```bash
python3 wechat_file_organizer.py --source /path/to/wechat/data --target /path/to/output
```

### 试运行模式

在实际执行前，可以先运行试运行模式查看会处理哪些文件：

```bash
python3 wechat_file_organizer.py --dry-run
```

### 查看帮助

```bash
python3 wechat_file_organizer.py --help
```

## 目录结构

归档后的目录结构如下：

```
/Users/<USER>/Downloads/wechat_files/
├── images/          # 图片文件
├── documents/       # 文档文件
├── videos/          # 视频文件
├── audio/           # 音频文件
├── archives/        # 压缩文件
└── others/          # 其他类型文件
```

## 注意事项

1. **备份重要数据**: 在运行工具前，建议先备份重要的微信数据
2. **磁盘空间**: 确保目标目录有足够的磁盘空间
3. **权限问题**: 可能需要给予脚本访问微信数据目录的权限
4. **运行时间**: 由于文件数量较多，首次运行可能需要较长时间

## 系统要求

- macOS 系统
- Python 3.6+
- 足够的磁盘空间用于存储归档文件

## 日志文件

运行过程中会生成 `wechat_organizer.log` 日志文件，记录详细的操作信息。

## 故障排除

如果遇到权限问题，可以尝试：

1. 在"系统偏好设置" > "安全性与隐私" > "隐私"中，给予终端或Python完全磁盘访问权限
2. 或者使用 `sudo` 运行脚本（不推荐）

## 示例输出

```
2024-08-16 19:30:00 - INFO - 开始微信文件归档任务
2024-08-16 19:30:00 - INFO - 源目录: /Users/<USER>/Library/Containers/com.tencent.xinWeChat/...
2024-08-16 19:30:00 - INFO - 目标目录: /Users/<USER>/Downloads/wechat_files
2024-08-16 19:30:01 - INFO - 目标目录创建完成: /Users/<USER>/Downloads/wechat_files
2024-08-16 19:30:01 - INFO - 开始扫描目录: /Users/<USER>/Library/Containers/com.tencent.xinWeChat/...
2024-08-16 19:30:10 - INFO - 已处理 1000 个文件...
...
==================================================
归档完成！统计信息:
总文件数: 188873
成功复制: 15420
重复文件: 165234
错误文件: 8219

各类别文件统计:
  images: 12450 个文件
  documents: 1230 个文件
  videos: 890 个文件
  audio: 650 个文件
  archives: 120 个文件
  others: 80 个文件
==================================================
```
