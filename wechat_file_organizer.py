#!/usr/bin/env python3
"""
微信聊天附件归档工具
扫描微信聊天记录目录，将相同文件类型的文件归档到相同类型的目录下
相同文件的只留一份（基于文件内容的MD5哈希值去重）
"""

import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from collections import defaultdict
import argparse
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wechat_organizer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WeChatFileOrganizer:
    def __init__(self, source_dir, target_dir):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.file_hashes = {}  # 存储文件哈希值，用于去重
        self.stats = {
            'total_files': 0,
            'copied_files': 0,
            'duplicate_files': 0,
            'error_files': 0,
            'categories': defaultdict(int)
        }
        
        # 文件类型分类映射
        self.file_categories = {
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'],
            'documents': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf'],
            'videos': ['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v'],
            'audio': ['.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a', '.wma'],
            'archives': ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'],
            'others': []  # 其他类型文件
        }
        
        # 创建目标目录
        self.create_target_directories()
    
    def create_target_directories(self):
        """创建目标目录结构"""
        try:
            self.target_dir.mkdir(parents=True, exist_ok=True)
            for category in self.file_categories.keys():
                category_dir = self.target_dir / category
                category_dir.mkdir(exist_ok=True)
            logger.info(f"目标目录创建完成: {self.target_dir}")
        except Exception as e:
            logger.error(f"创建目标目录失败: {e}")
            raise
    
    def get_file_hash(self, file_path):
        """计算文件的MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None
    
    def get_file_category(self, file_path):
        """根据文件扩展名确定文件类别"""
        ext = file_path.suffix.lower()
        for category, extensions in self.file_categories.items():
            if ext in extensions:
                return category
        return 'others'
    
    def is_system_file(self, file_path):
        """判断是否为系统文件或不需要归档的文件"""
        # 排除系统文件和特定目录
        exclude_patterns = [
            '.DS_Store',
            'Thumbs.db',
            '.tmp',
            '.cache',
            'StickerStoreData',  # 微信表情包数据
            'Avatar',  # 头像目录
            'CGI',
            'KeyValue',
            'MMappedKV'
        ]
        
        file_str = str(file_path)
        for pattern in exclude_patterns:
            if pattern in file_str:
                return True
        
        # 排除小于1KB的文件（可能是缓存或临时文件）
        try:
            if file_path.stat().st_size < 1024:
                return True
        except:
            return True
            
        return False
    
    def copy_file_safely(self, source_file, target_file):
        """安全地复制文件，避免覆盖"""
        if target_file.exists():
            # 如果目标文件已存在，添加数字后缀
            counter = 1
            stem = target_file.stem
            suffix = target_file.suffix
            parent = target_file.parent
            
            while target_file.exists():
                target_file = parent / f"{stem}_{counter}{suffix}"
                counter += 1
        
        try:
            shutil.copy2(source_file, target_file)
            return target_file
        except Exception as e:
            logger.error(f"复制文件失败 {source_file} -> {target_file}: {e}")
            return None
    
    def scan_and_organize(self, dry_run=False):
        """扫描源目录并组织文件"""
        logger.info(f"开始扫描目录: {self.source_dir}")

        # 使用glob递归查找所有文件
        for file_path in self.source_dir.rglob('*'):
            if not file_path.is_file():
                continue

            self.stats['total_files'] += 1

            # 跳过系统文件
            if self.is_system_file(file_path):
                continue

            # 获取文件类别
            category = self.get_file_category(file_path)

            # 只有在非试运行模式下才计算哈希值（耗时操作）
            if not dry_run:
                # 计算文件哈希值
                file_hash = self.get_file_hash(file_path)
                if file_hash is None:
                    self.stats['error_files'] += 1
                    continue

                # 检查是否为重复文件
                if file_hash in self.file_hashes:
                    self.stats['duplicate_files'] += 1
                    logger.debug(f"发现重复文件: {file_path}")
                    continue

                # 记录文件哈希值
                self.file_hashes[file_hash] = file_path

                # 确定目标文件路径
                target_category_dir = self.target_dir / category
                target_file = target_category_dir / file_path.name

                # 复制文件
                copied_file = self.copy_file_safely(file_path, target_file)
                if copied_file:
                    self.stats['copied_files'] += 1
                    self.stats['categories'][category] += 1
                    logger.debug(f"复制文件: {file_path} -> {copied_file}")
                else:
                    self.stats['error_files'] += 1
            else:
                # 试运行模式，只统计不复制
                self.stats['categories'][category] += 1

            # 每处理1000个文件输出一次进度
            if self.stats['total_files'] % 1000 == 0:
                logger.info(f"已处理 {self.stats['total_files']} 个文件...")
    
    def print_summary(self):
        """打印归档统计信息"""
        logger.info("=" * 50)
        logger.info("归档完成！统计信息:")
        logger.info(f"总文件数: {self.stats['total_files']}")
        logger.info(f"成功复制: {self.stats['copied_files']}")
        logger.info(f"重复文件: {self.stats['duplicate_files']}")
        logger.info(f"错误文件: {self.stats['error_files']}")
        logger.info("\n各类别文件统计:")
        for category, count in self.stats['categories'].items():
            logger.info(f"  {category}: {count} 个文件")
        logger.info("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='微信聊天附件归档工具')
    parser.add_argument(
        '--source',
        default='/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9',
        help='微信聊天记录源目录'
    )
    parser.add_argument(
        '--target',
        default='/Users/<USER>/Downloads/wechat_files',
        help='归档目标目录'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅扫描不复制文件'
    )
    
    args = parser.parse_args()
    
    # 检查源目录是否存在
    if not os.path.exists(args.source):
        logger.error(f"源目录不存在: {args.source}")
        return 1
    
    logger.info(f"开始微信文件归档任务")
    logger.info(f"源目录: {args.source}")
    logger.info(f"目标目录: {args.target}")
    
    try:
        organizer = WeChatFileOrganizer(args.source, args.target)

        if args.dry_run:
            logger.info("执行试运行模式（不会实际复制文件）")

        organizer.scan_and_organize(dry_run=args.dry_run)
        organizer.print_summary()
        
        logger.info("归档任务完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"归档过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
